import { useSearchCustomer } from '@/apis/customer/customer.api';
import {
    SearchCustomer,
    SearchCustomerResponse,
} from '@/apis/customer/customer.type';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { LeadStatus as LeadStatusData } from '@/constants/sharedData/sharedData';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import {
    <PERSON>ton,
    Card,
    CardHeader,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Table,
    Input,
} from 'reactstrap';

interface AddCustomerModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: (selectedCustomers: SearchCustomerResponse[]) => void;
}

const AddCustomerModal = ({
    isOpen,
    toggle,
    onSuccess,
}: AddCustomerModalProps) => {
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedData, setSelectedData] = useState<SearchCustomerResponse[]>(
        [],
    );
    const methods = useForm<SearchCustomer>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
        },
    });
    const { control } = methods;
    const [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'IndustryId',
            'BusinessType',
            'LeadStatus',
            'SalePerson',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });
    const { data, isLoading } = useSearchCustomer({
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });
    const { items: listCustomer = [] } = data ?? {};
    const formattedCustomer = listCustomer.map(
        (Customer: SearchCustomerResponse) => ({
            ...Customer,
            createdOn: Customer.createdOn
                ? new Date(
                      Customer.createdOn.substring(0, 10),
                  ).toLocaleDateString('vi-VN')
                : '',
        }),
    );

    // Filter out already selected customers
    const availableCustomers = formattedCustomer.filter(
        (customer) => !selectedIds.includes(customer.id),
    );

    // Handle checkbox selection
    const handleSelectCustomer = (customer: SearchCustomerResponse) => {
        const isSelected = selectedIds.includes(customer.id);
        if (isSelected) {
            setSelectedIds(selectedIds.filter((id) => id !== customer.id));
            setSelectedData(
                selectedData.filter((item) => item.id !== customer.id),
            );
        } else {
            setSelectedIds([...selectedIds, customer.id]);
            setSelectedData([...selectedData, customer]);
        }
    };

    // Handle select all checkbox
    const handleSelectAll = () => {
        if (availableCustomers.length === 0) {
            return;
        }

        const allAvailableSelected = availableCustomers.every((customer) =>
            selectedIds.includes(customer.id),
        );

        if (allAvailableSelected) {
            // Unselect all available customers
            const availableIds = availableCustomers.map((item) => item.id);
            setSelectedIds(selectedIds.filter((id) => !availableIds.includes(id)));
            setSelectedData(selectedData.filter((item) => !availableIds.includes(item.id)));
        } else {
            // Select all available customers
            const allIds = availableCustomers.map((item) => item.id);
            setSelectedIds([...selectedIds, ...allIds]);
            setSelectedData([...selectedData, ...availableCustomers]);
        }
    };

    // Check if all available customers are selected
    const isAllAvailableSelected = availableCustomers.length > 0 &&
        availableCustomers.every((customer) => selectedIds.includes(customer.id));

    const handleCancel = () => {
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    const handleSave = () => {
        if (onSuccess && selectedData.length > 0) {
            onSuccess(selectedData);
        }
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} size='xl'>
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>Thêm khách hàng</ModalHeader>
                <ModalBody>
                    <Col lg={12}>
                        <Card>
                            <CardHeader>
                                <div className='d-flex flex-wrap align-items-center gap-2'>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                    />
                                </div>
                            </CardHeader>

                            {isLoading ? (
                                <div className='text-center p-4'>
                                    <div
                                        className='spinner-border'
                                        role='status'
                                    >
                                        <span className='visually-hidden'>
                                            Loading...
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    <Table responsive striped bordered hover>
                                        <thead>
                                            <tr>
                                                <th style={{ width: '50px' }}>
                                                    <Input
                                                        type='checkbox'
                                                        checked={isAllAvailableSelected}
                                                        onChange={
                                                            handleSelectAll
                                                        }
                                                        disabled={
                                                            availableCustomers.length ===
                                                            0
                                                        }
                                                    />
                                                </th>
                                                <th>Tên khách hàng</th>
                                                <th>Loại hình</th>
                                                <th>Lĩnh vực</th>
                                                <th>Giai đoạn</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {availableCustomers.length > 0 ? (
                                                availableCustomers.map(
                                                    (customer) => {
                                                        const status =
                                                            LeadStatusData.find(
                                                                (s) =>
                                                                    s.value ===
                                                                    customer.leadStatus,
                                                            );

                                                        return (
                                                            <tr
                                                                key={
                                                                    customer.id
                                                                }
                                                            >
                                                                <td>
                                                                    <Input
                                                                        type='checkbox'
                                                                        checked={
                                                                            selectedIds.includes(customer.id)
                                                                        }
                                                                        onChange={() =>
                                                                            handleSelectCustomer(
                                                                                customer,
                                                                            )
                                                                        }
                                                                    />
                                                                </td>
                                                                <td>
                                                                    {
                                                                        customer.name
                                                                    }
                                                                </td>
                                                                <td>
                                                                    {
                                                                        customer.businessTypeName
                                                                    }
                                                                </td>
                                                                <td>
                                                                    {
                                                                        customer.industryName
                                                                    }
                                                                </td>
                                                                <td>
                                                                    <span
                                                                        className='badge me-1'
                                                                        style={{
                                                                            backgroundColor:
                                                                                '#daf4f0',
                                                                            color: '#2fbeab',
                                                                            display:
                                                                                'inline-block',
                                                                            textAlign:
                                                                                'center',
                                                                            padding:
                                                                                '4px 8px',
                                                                            fontSize:
                                                                                '12px',
                                                                            fontWeight: 500,
                                                                            borderRadius:
                                                                                '4px',
                                                                        }}
                                                                    >
                                                                        {status?.label ||
                                                                            ''}
                                                                    </span>
                                                                </td>
                                                            </tr>
                                                        );
                                                    },
                                                )
                                            ) : (
                                                <tr>
                                                    <td
                                                        colSpan={5}
                                                        className='text-center'
                                                    >
                                                        {selectedIds.length > 0
                                                            ? 'Tất cả khách hàng đã được chọn'
                                                            : 'Không có dữ liệu'}
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </Table>
                                </>
                            )}
                        </Card>
                    </Col>
                </ModalBody>
                <ModalFooter>
                    <Button color='danger' onClick={handleCancel}>
                        Huỷ
                    </Button>
                    <Button
                        color='success'
                        onClick={handleSave}
                        disabled={selectedData.length === 0}
                    >
                        Lưu
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddCustomerModal;
